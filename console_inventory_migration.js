/**
 * Console Inventory Migration Script
 * Run this directly in the browser console on your Bento application
 * Paste this entire script and run it to generate the migration data
 */

// All IDs from your BEO debug log
const ALL_INVENTORY_IDS = [
    20456859, // Main combination
    20435097, 20434616, 20434630, 20434495, 20434052, // Ingredient groups
    // All choice IDs
    20434612, 20434593, 20434595, 20434613, 20434614, 20434602, 20434592, 20434622,
    20434617, 20434620, 20434621, 20434629, 20434625, 20434626, 20434623, 20434633,
    20434632, 20434636, 20434637, 20434990, 20434487, 20434498, 20434490, 20434472,
    20434475, 20434471, 20434483, 20434502, 20434478, 20434486, 20434477, 20434488,
    20434481, 20434482, 20434474, 20434479, 20434470, 20434493, 20434516, 20434500,
    20434480, 20434484, 20434485, 20434501, 20434491, 20434489, 20434499, 20433663,
    20434586, 20434587, 20434590, 20434030, 20434001, 20434053, 20434020, 20433997,
    20434017, 20434018, 20434116, 20433999, 20433739, 20434023, 20434015, 20433998,
    20434003, 20434021, 20434035, 20434004, 20434014, 20434022, 20434000, 20434002,
    20434007, 20434008, 20434026, 20434010, 20434057, 20434103, 20434006, 20434024,
    20434073, 20434012, 20434013, 20434075, 20434016, 20434193, 20434029, 20434028,
    20434027, 20434033, 20434019, 20434025, 20434009, 20433827, 20434056, 20434110,
    20434005, 20434064, 20434071, 20434084, 20434087, 20434101, 20434076, 20433992,
    20434081, 20434077, 20434037, 20433991, 20434048, 20434112, 20434105, 20434109,
    20432542, 20434104, 20434113, 20434047, 20434145, 20434079, 20434118, 20434125,
    20434126, 20434130, 20434152, 20434160, 20434158, 20434161, 20434162, 20434166,
    20434167, 20434168, 20434165, 20434181, 20434092, 20434153, 20434171, 20434156,
    20434050, 20434173
];

// Global storage
window.inventoryMigrationData = [];
let currentIndex = 0;

console.log('🚀 Starting Inventory Migration Script');
console.log(`📊 Total IDs to process: ${ALL_INVENTORY_IDS.length}`);

function processNextItem() {
    if (currentIndex >= ALL_INVENTORY_IDS.length) {
        generateMigrationOutput();
        return;
    }
    
    const currentId = ALL_INVENTORY_IDS[currentIndex];
    console.log(`⏳ Processing ${currentIndex + 1}/${ALL_INVENTORY_IDS.length}: ID ${currentId}`);
    
    // Try combinations first
    databaseConnection.obj.getById('inventory_billable_combinations', currentId, function(combo) {
        if (combo && combo.id) {
            console.log(`✅ Found combination: ${combo.name}`);
            window.inventoryMigrationData.push({
                type: 'inventory_billable_combinations',
                original_id: combo.id,
                data: combo
            });
            currentIndex++;
            setTimeout(processNextItem, 50); // Small delay
        } else {
            // Try groups
            databaseConnection.obj.getById('inventory_billable_groups', currentId, function(group) {
                if (group && group.id) {
                    console.log(`✅ Found group: ${group.name}`);
                    window.inventoryMigrationData.push({
                        type: 'inventory_billable_groups',
                        original_id: group.id,
                        data: group
                    });
                } else {
                    console.log(`❌ ID ${currentId} not found`);
                }
                currentIndex++;
                setTimeout(processNextItem, 50); // Small delay
            });
        }
    });
}

function generateMigrationOutput() {
    console.log('\n🎉 PROCESSING COMPLETE!');
    console.log(`📈 Successfully fetched ${window.inventoryMigrationData.length} records`);
    
    // Prepare migration data
    const migrationRecords = window.inventoryMigrationData.map(item => {
        const data = { ...item.data };
        data.data_source_id = data.id; // Set data_source_id to original ID
        // Keep same ID for consistency
        return {
            table: item.type,
            id: data.id,
            data: data
        };
    });
    
    // Generate SQL
    console.log('\n📝 GENERATING SQL MIGRATION...');
    
    const sqlLines = [
        '-- Inventory Migration SQL',
        `-- Generated: ${new Date().toISOString()}`,
        `-- Total records: ${migrationRecords.length}`,
        '-- Note: data_source_id = original ID, table ID = JSON ID',
        ''
    ];
    
    // Group by table type
    const combinations = migrationRecords.filter(r => r.table === 'inventory_billable_combinations');
    const groups = migrationRecords.filter(r => r.table === 'inventory_billable_groups');
    
    if (combinations.length > 0) {
        sqlLines.push(`-- Inventory Billable Combinations (${combinations.length} records)`);
        combinations.forEach(record => {
            const jsonStr = JSON.stringify(record.data).replace(/'/g, "''");
            sqlLines.push(`INSERT INTO inventory_billable_combinations (id, object_data) VALUES (${record.id}, '${jsonStr}');`);
        });
        sqlLines.push('');
    }
    
    if (groups.length > 0) {
        sqlLines.push(`-- Inventory Billable Groups (${groups.length} records)`);
        groups.forEach(record => {
            const jsonStr = JSON.stringify(record.data).replace(/'/g, "''");
            sqlLines.push(`INSERT INTO inventory_billable_groups (id, object_data) VALUES (${record.id}, '${jsonStr}');`);
        });
    }
    
    // Output results
    console.log('\n🗂️ JSON DATA (stored in window.inventoryMigrationData):');
    console.log(JSON.stringify(migrationRecords, null, 2));
    
    console.log('\n📋 SQL MIGRATION (copy to pgAdmin):');
    console.log('='.repeat(80));
    sqlLines.forEach(line => console.log(line));
    console.log('='.repeat(80));
    
    // Store SQL for easy copying
    window.migrationSQL = sqlLines.join('\n');
    
    console.log('\n✨ MIGRATION COMPLETE!');
    console.log('💾 Data stored in: window.inventoryMigrationData');
    console.log('📄 SQL stored in: window.migrationSQL');
    console.log('📋 Copy window.migrationSQL to run in pgAdmin');
}

// Start the process
processNextItem();
