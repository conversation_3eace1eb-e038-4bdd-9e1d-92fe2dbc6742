/**
 * <PERSON><PERSON><PERSON> to SQL Converter for Database Migration
 * Converts the downloaded JSON array to SQL INSERT statements
 * 
 * Instructions:
 * 1. Open your browser console
 * 2. Paste this entire script
 * 3. When prompted, paste your JSON data
 * 4. Copy the generated SQL to pgAdmin
 */

console.log('🔄 JSON to SQL Converter Ready');
console.log('📋 Please paste your JSON array data when prompted...');

// Function to convert JSON array to SQL
function convertJSONToSQL(jsonArray) {
    if (!Array.isArray(jsonArray)) {
        console.error('❌ Error: Data must be an array of objects');
        return;
    }
    
    console.log(`📊 Processing ${jsonArray.length} objects...`);
    
    const timestamp = new Date().toISOString();
    let sql = `-- Database Migration Script
-- Generated: ${timestamp}
-- Total Records: ${jsonArray.length}
-- Source: production_objects.json
-- Target Table: objects
-- 
-- Instructions:
-- 1. Copy this entire SQL block
-- 2. Go to pgAdmin > SQL command
-- 3. Paste and execute
--

BEGIN;

`;

    let successCount = 0;
    let errorCount = 0;

    jsonArray.forEach((obj, index) => {
        try {
            // Ensure we have required fields
            if (!obj.id) {
                console.warn(`⚠️ Object ${index + 1} missing ID, skipping`);
                errorCount++;
                return;
            }

            // Prepare the object data with migration fields
            const migrationObject = {
                ...obj,
                data_source_id: obj.id, // Original ID becomes data_source_id
                id: obj.id // Keep same ID for consistency
            };

            // Determine object type
            const objectType = obj.object_bp_type || obj.objectType || 'unknown';
            
            // Escape single quotes in JSON
            const jsonData = JSON.stringify(migrationObject).replace(/'/g, "''");
            
            // Generate INSERT statement
            sql += `-- Record ${index + 1}: ${objectType} (ID: ${obj.id})
INSERT INTO objects (
    id, 
    instance, 
    object_type, 
    object_data, 
    date_created, 
    is_deleted
) VALUES (
    ${obj.id},
    'infinity',
    '${objectType}',
    '${jsonData}',
    NOW(),
    false
);

`;
            successCount++;
            
        } catch (error) {
            console.error(`❌ Error processing object ${index + 1}:`, error);
            errorCount++;
        }
    });

    sql += `COMMIT;

-- Migration Summary:
-- Successfully processed: ${successCount} records
-- Errors: ${errorCount} records
-- Object types found: ${[...new Set(jsonArray.map(obj => obj.object_bp_type || obj.objectType || 'unknown'))].join(', ')}
-- ID range: ${Math.min(...jsonArray.filter(obj => obj.id).map(obj => obj.id))} - ${Math.max(...jsonArray.filter(obj => obj.id).map(obj => obj.id))}

-- Next steps:
-- 1. Copy this SQL to pgAdmin
-- 2. Go to SQL command tab
-- 3. Paste and execute
-- 4. Check for any errors in the output
`;

    console.log('✅ SQL Generation Complete!');
    console.log(`📈 Processed: ${successCount} records`);
    console.log(`❌ Errors: ${errorCount} records`);
    console.log('');
    console.log('📋 COPY THE SQL BELOW TO PGADMIN:');
    console.log('='.repeat(80));
    console.log(sql);
    console.log('='.repeat(80));
    
    // Store in global variable for easy access
    window.generatedSQL = sql;
    
    return sql;
}

// Interactive prompt for JSON data
console.log('');
console.log('🎯 Ready to convert your JSON!');
console.log('');
console.log('📝 STEP 1: Open your production_objects.json file');
console.log('📝 STEP 2: Copy the ENTIRE contents (should start with [ and end with ])');
console.log('📝 STEP 3: Run this command:');
console.log('');
console.log('convertJSONToSQL(PASTE_YOUR_JSON_HERE)');
console.log('');
console.log('💡 Example:');
console.log('convertJSONToSQL([{"id": 123, "name": "test", ...}, {...}])');
console.log('');
console.log('🔧 Alternative: If you have the JSON in a variable, use:');
console.log('convertJSONToSQL(yourVariableName)');
