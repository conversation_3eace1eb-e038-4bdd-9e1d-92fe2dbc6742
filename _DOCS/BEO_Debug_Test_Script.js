/**
 * BEO Debug Test Script
 * 
 * Run this in the browser console to trigger BEO merge tag generation
 * with debugging enabled. Results will be <NAME_EMAIL>
 * 
 * Based on handoff document test case:
 * - Duplicated Category: 20456830 ("Food - 2025 Pricing (*)")
 * - Test Combination: 20456859 ("Buffet Entree* (*)")
 */

console.log('=== BEO DEBUG TEST SCRIPT ===');

// Test with the problematic duplicated category
function testBEOWithDuplicatedCategory() {
    console.log('Testing BEO with duplicated category 20456830...');
    
    // You'll need to replace MENU_ID with an actual menu ID that contains
    // items from the duplicated category
    const MENU_ID = 'YOUR_MENU_ID_HERE'; // Replace with actual menu ID
    
    sb.data.db.controller('generateInfinityBEOMergeTag', {
        menuId: MENU_ID,
        options: [20456830], // Duplicated category "Food - 2025 Pricing (*)"
        setup: {
            border: true,
            sectionNames: true,
            internalNotes: true,
            tz: Intl.DateTimeFormat().resolvedOptions().timeZone,
            choiceItems: true
        }
    }, function(mergeTagHTML) {
        console.log('BEO Merge Tag Generated');
        console.log('HTML Length:', mergeTagHTML.length);
        console.log('Check email for detailed debug log');
        
        // Display the result in a new window for visual inspection
        const newWindow = window.open('', '_blank');
        newWindow.document.write(`
            <html>
                <head><title>BEO Debug Result</title></head>
                <body>
                    <h1>BEO Merge Tag Result</h1>
                    <p>Category: 20456830 (Food - 2025 Pricing (*))</p>
                    <hr>
                    ${mergeTagHTML}
                </body>
            </html>
        `);
    });
}

// Test with the working original category for comparison
function testBEOWithOriginalCategory() {
    console.log('Testing BEO with original category 1162747...');
    
    const MENU_ID = 'YOUR_MENU_ID_HERE'; // Replace with actual menu ID
    
    sb.data.db.controller('generateInfinityBEOMergeTag', {
        menuId: MENU_ID,
        options: [1162747], // Original working category
        setup: {
            border: true,
            sectionNames: true,
            internalNotes: true,
            tz: Intl.DateTimeFormat().resolvedOptions().timeZone,
            choiceItems: true
        }
    }, function(mergeTagHTML) {
        console.log('BEO Merge Tag Generated (Original)');
        console.log('HTML Length:', mergeTagHTML.length);
        console.log('Check email for detailed debug log');
        
        // Display the result in a new window for visual inspection
        const newWindow = window.open('', '_blank');
        newWindow.document.write(`
            <html>
                <head><title>BEO Debug Result - Original</title></head>
                <body>
                    <h1>BEO Merge Tag Result - Original Category</h1>
                    <p>Category: 1162747 (Original working category)</p>
                    <hr>
                    ${mergeTagHTML}
                </body>
            </html>
        `);
    });
}

// Quick data verification functions from handoff document
function verifyDuplicatedCombination() {
    console.log('Verifying duplicated combination 20456859...');
    
    databaseConnection.obj.getById('inventory_billable_combinations', 20456859, function(combo) {
        console.log('=== COMBINATION VERIFICATION ===');
        console.log('Name:', combo.name);
        console.log('Category:', combo.category);
        console.log('Items count:', combo.items?.length || 0);
        
        if (combo.items) {
            combo.items.forEach((item, i) => {
                console.log(`  Item ${i+1}: inventory_group = ${item.inventory_group}`);
            });
        }
        
        console.log('=== END VERIFICATION ===');
    });
}

function verifyReferencedGroups() {
    console.log('Verifying referenced groups have choices...');
    
    const groupIds = [20434630, 20434495, 20434052]; // From handoff document
    
    groupIds.forEach(groupId => {
        databaseConnection.obj.getById('inventory_billable_groups', groupId, function(group) {
            const choiceCount = group.items?.[0]?.choices?.length || 0;
            console.log(`${group.name}: ${choiceCount} choices`);
        });
    });
}

// Main test function
function runBEODebugTest() {
    console.log('=== STARTING BEO DEBUG TEST ===');
    console.log('1. First verify data integrity...');
    
    verifyDuplicatedCombination();
    verifyReferencedGroups();
    
    console.log('2. Testing BEO generation...');
    console.log('   NOTE: You need to set MENU_ID in the script first!');
    console.log('   Replace YOUR_MENU_ID_HERE with an actual menu ID');
    
    // Uncomment these when you have a menu ID:
    // testBEOWithDuplicatedCategory();
    // setTimeout(() => testBEOWithOriginalCategory(), 5000); // Wait 5 seconds between tests
}

// Instructions
console.log('=== INSTRUCTIONS ===');
console.log('1. Edit this script and replace YOUR_MENU_ID_HERE with an actual menu ID');
console.log('2. Run: runBEODebugTest()');
console.log('3. Check your email for detailed debug logs');
console.log('4. Compare results between duplicated and original categories');

// Auto-run data verification
verifyDuplicatedCombination();
verifyReferencedGroups();
