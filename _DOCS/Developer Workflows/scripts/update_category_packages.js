// Global array to collect missing groups across all combinations
var globalMissingGroups = [];

// Global array to collect missing groups that need to be created
var globalMissingGroups = [];

/**
 * <PERSON><PERSON>t to update inventory_group references in copied combinations
 * to point to the copied groups instead of the original groups
 *
 * @param {boolean} dryRun - If true, only logs changes without updating records
 * @param {number} limit - Limit processing to this many combinations (for testing)
 * @param {number} startIndex - Skip this many combinations before starting (default: 0)
 * @param {Array<number>} categoryIds - Array of category IDs to process
 */
function updateCombinationGroupReferences(dryRun = true, limit = 2, startIndex = 0, categoryIds = [20456830]) {
  console.log(`\n========== STARTING GROUP REFERENCE UPDATE PROCESS ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will update records)'}`);
  console.log(`Processing: ${limit} combinations starting from index ${startIndex}`);
  console.log(`Target Category IDs: ${categoryIds.join(', ')}`);
  console.log(`=============================================================\n`);

  // Reset global missing groups array
  globalMissingGroups = [];

  // Process each category ID
  processCategoriesSequentially(categoryIds, 0, dryRun, limit, startIndex);
}

/**
 * SEPARATE FUNCTION: Create missing groups from the collected list
 * @param {boolean} dryRun - If true, only logs what would be created
 */
function createMissingGroupsFromList(dryRun = true) {
  console.log(`\n========== CREATING MISSING GROUPS FROM COLLECTED LIST ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will create records)'}`);
  console.log(`Found ${globalMissingGroups.length} missing groups to create`);
  console.log(`================================================================\n`);

  if (globalMissingGroups.length === 0) {
    console.log(`No missing groups to create.`);
    return;
  }

  // Remove duplicates based on ID
  const uniqueMissingGroups = globalMissingGroups.filter((group, index, self) =>
    index === self.findIndex(g => g.id === group.id)
  );

  console.log(`After removing duplicates: ${uniqueMissingGroups.length} unique groups to create`);

  uniqueMissingGroups.forEach((group, index) => {
    console.log(`  ${index + 1}. "${group.name}" (ID: ${group.id}) → "${group.name} (*)"`);
  });

  // Process groups sequentially
  processGroupCreation(uniqueMissingGroups, 0, dryRun);
}

/**
 * Process category IDs one at a time
 */
function processCategoriesSequentially(categoryIds, categoryIndex, dryRun, limit, startIndex) {
  if (categoryIndex >= categoryIds.length) {
    console.log(`\n========== ALL CATEGORIES PROCESSED ==========`);

    // Show final summary of missing groups
    showMissingGroupsSummary();
    return;
  }

  const categoryId = categoryIds[categoryIndex];
  console.log(`\n========== PROCESSING CATEGORY ${categoryIndex + 1}/${categoryIds.length} ==========`);
  console.log(`Category ID: ${categoryId}`);

  // Query combinations for this category
  databaseConnection.obj.getWhere('inventory_billable_combinations', {
    category: categoryId
  }, function(combinations) {
    console.log(`Found ${combinations.length} combinations in category ${categoryId}:`);

    combinations.forEach((combo, index) => {
      console.log(`  ${index + 1}. "${combo.name}" (ID: ${combo.id})`);
    });

    console.log(`\n--- Starting detailed analysis of combinations ---`);

    // Process each combination with getById to get full details
    processCombinationDetails(combinations, 0, categoryId, limit, startIndex);
  });
}

/**
 * Process combination details one at a time using getById
 */
function processCombinationDetails(combinations, comboIndex, categoryId, limit, startIndex) {
  if (comboIndex >= combinations.length) {
    console.log(`\n--- Completed detailed analysis for category ${categoryId} ---`);
    return;
  }

  const combo = combinations[comboIndex];
  console.log(`\n========== COMBINATION DETAILS ${comboIndex + 1}/${combinations.length} ==========`);
  console.log(`Getting full details for: "${combo.name}" (ID: ${combo.id})`);

  // Get full combination details using getById
  databaseConnection.obj.getById('inventory_billable_combinations', combo.id, function(fullCombo) {
    console.log(`Full combination data retrieved for "${fullCombo.name}"`);

    // Process items array and find ALL inventory_group references
    if (fullCombo.items && Array.isArray(fullCombo.items)) {
      console.log(`Found ${fullCombo.items.length} items in combination:`);

      // Collect all inventory_group IDs from the entire structure
      const allInventoryGroupIds = [];

      fullCombo.items.forEach((item, itemIndex) => {
        console.log(`\n  Item ${itemIndex + 1}:`);
        console.log(`    ID: ${item.id}`);
        console.log(`    Name: "${item.name || 'Unnamed item'}"`);
        console.log(`    Max Selections: ${item.max_selections}`);

        // Check for direct inventory_group reference
        if (item.inventory_group && item.inventory_group !== 0) {
          console.log(`    Direct Inventory Group ID: ${item.inventory_group}`);
          allInventoryGroupIds.push({
            id: item.inventory_group,
            location: `items[${itemIndex}].inventory_group`,
            itemIndex: itemIndex,
            choiceIndex: null
          });
        } else {
          console.log(`    Direct Inventory Group ID: ${item.inventory_group || 'None'}`);
        }

        // Check for choices array and inventory_group references within choices
        if (item.choices && Array.isArray(item.choices)) {
          console.log(`    Choices Array Length: ${item.choices.length}`);

          item.choices.forEach((choice, choiceIndex) => {
            console.log(`      Choice ${choiceIndex + 1}:`);
            console.log(`        Choice ID: ${choice.id}`);
            console.log(`        Choice Name: "${choice.name || 'Unnamed choice'}"`);

            if (choice.inventory_group && choice.inventory_group !== 0) {
              console.log(`        Choice Inventory Group ID: ${choice.inventory_group}`);
              allInventoryGroupIds.push({
                id: choice.inventory_group,
                location: `items[${itemIndex}].choices[${choiceIndex}].inventory_group`,
                itemIndex: itemIndex,
                choiceIndex: choiceIndex
              });
            } else {
              console.log(`        Choice Inventory Group ID: ${choice.inventory_group || 'None'}`);
            }
          });
        } else {
          console.log(`    Choices Array: None`);
        }
      });

      console.log(`\n  SUMMARY: Found ${allInventoryGroupIds.length} total inventory_group references:`);
      allInventoryGroupIds.forEach((ref, index) => {
        console.log(`    ${index + 1}. ID ${ref.id} at ${ref.location}`);
      });

      // Now get details for each inventory group and build mapping
      buildGroupMappingForCombination(allInventoryGroupIds, 0, fullCombo, {}, combinations, comboIndex, categoryId, limit, startIndex);
    } else {
      console.log(`No items array found in combination "${fullCombo.name}"`);

      // Process next combination
      processCombinationDetails(combinations, comboIndex + 1, categoryId, limit, startIndex);
    }
  });
}

/**
 * Build group mapping for this combination by finding copy groups for each original group
 */
function buildGroupMappingForCombination(inventoryGroupRefs, refIndex, fullCombo, groupMapping, combinations, comboIndex, categoryId, limit, startIndex, missingGroups = []) {
  if (refIndex >= inventoryGroupRefs.length) {
    console.log(`\n--- Completed group mapping analysis for "${fullCombo.name}" ---`);
    console.log(`Group mapping built:`, groupMapping);
    console.log(`Missing groups found: ${missingGroups.length}`);

    if (missingGroups.length > 0) {
      console.log(`\n--- MISSING GROUPS FOUND FOR THIS COMBINATION ---`);
      missingGroups.forEach((group, index) => {
        console.log(`  ${index + 1}. "${group.name}" (ID: ${group.id}) → needs "${group.name} (*)"`);
      });
      console.log(`These groups have been added to the global missing groups list.`);
    }

    // Always proceed with update using available mappings
    updateCombinationWithNewGroupIds(fullCombo, groupMapping, combinations, comboIndex, categoryId, limit, startIndex);
    return;
  }

  const ref = inventoryGroupRefs[refIndex];
  console.log(`\n    Processing group reference ${refIndex + 1}/${inventoryGroupRefs.length}:`);
  console.log(`      Location: ${ref.location}`);
  console.log(`      Group ID: ${ref.id}`);

  // Get the original group details
  databaseConnection.obj.getById('inventory_billable_groups', ref.id, function(originalGroup) {
    console.log(`      Original Group Name: "${originalGroup.name}"`);
    console.log(`      Has "(*)" in name: ${originalGroup.name.includes('(*)')}`);

    if (originalGroup.name.includes('(*)')) {
      // This is already a copy group, no need to map
      console.log(`      ✅ Already a copy group, no mapping needed`);
      buildGroupMappingForCombination(inventoryGroupRefs, refIndex + 1, fullCombo, groupMapping, combinations, comboIndex, categoryId, limit, startIndex, missingGroups);
    } else {
      // This is an original group, find its copy
      const copyGroupName = originalGroup.name + ' (*)';
      console.log(`      🔍 Looking for copy group: "${copyGroupName}"`);

      // Use contains query instead of exact match to handle special characters
      databaseConnection.obj.getWhere('inventory_billable_groups', {
        name: {
          type: 'contains',
          value: originalGroup.name
        }
      }, function(allMatchingGroups) {
        // Filter to find the exact copy group from the contains results
        const copyGroups = allMatchingGroups.filter(g =>
          g.name === copyGroupName ||
          g.name.trim() === copyGroupName.trim()
        );
        if (copyGroups && copyGroups.length > 0) {
          const copyGroup = copyGroups[0];
          groupMapping[originalGroup.id] = copyGroup.id;
          console.log(`      ✅ MAPPING FOUND: "${originalGroup.name}" (${originalGroup.id}) → "${copyGroup.name}" (${copyGroup.id})`);

          // Show detailed comparison
          console.log(`        ORIGINAL GROUP DETAILS:`);
          console.log(`          ID: ${originalGroup.id}`);
          console.log(`          Name: "${originalGroup.name}"`);
          console.log(`        COPY GROUP DETAILS:`);
          console.log(`          ID: ${copyGroup.id}`);
          console.log(`          Name: "${copyGroup.name}"`);
        } else {
          console.log(`      ⚠️ WARNING: No copy group found for "${originalGroup.name}"`);
          console.log(`      📝 Adding to global missing groups list`);

          // Add to both local and global missing groups
          missingGroups.push(originalGroup);
          globalMissingGroups.push(originalGroup);
        }

        // Continue with next reference
        buildGroupMappingForCombination(inventoryGroupRefs, refIndex + 1, fullCombo, groupMapping, combinations, comboIndex, categoryId, limit, startIndex, missingGroups);
      });
    }
  });
}

/**
 * Show summary of all missing groups collected during the process
 */
function showMissingGroupsSummary() {
  console.log(`\n========== MISSING GROUPS SUMMARY ==========`);

  if (globalMissingGroups.length === 0) {
    console.log(`✅ No missing groups found! All combinations already point to copy groups.`);
    return;
  }

  // Remove duplicates based on ID
  const uniqueMissingGroups = globalMissingGroups.filter((group, index, self) =>
    index === self.findIndex(g => g.id === group.id)
  );

  console.log(`Found ${globalMissingGroups.length} total missing group references`);
  console.log(`Found ${uniqueMissingGroups.length} unique missing groups that need to be created:`);
  console.log(`===========================================\n`);

  uniqueMissingGroups.forEach((group, index) => {
    console.log(`${index + 1}. "${group.name}" (ID: ${group.id})`);
    console.log(`   Category: ${group.category}`);
    console.log(`   Will become: "${group.name} (*)"`);
    console.log(``);
  });

  console.log(`\n🔧 NEXT STEPS:`);
  console.log(`1. Run: createMissingGroupsFromList(true)  // Dry run first`);
  console.log(`2. Run: createMissingGroupsFromList(false) // Create the groups`);
  console.log(`3. Re-run: updateCombinationGroupReferences() // Update combinations`);
}

/**
 * VERIFICATION FUNCTION: Check if missing groups actually exist but we're missing them
 * due to string comparison issues or special characters
 */
function verifyMissingGroupsFromList() {
  console.log(`\n========== VERIFYING MISSING GROUPS ==========`);

  if (globalMissingGroups.length === 0) {
    console.log(`❌ No missing groups in global list. Run updateCombinationGroupReferences() first.`);
    return;
  }

  // Remove duplicates based on ID
  const uniqueMissingGroups = globalMissingGroups.filter((group, index, self) =>
    index === self.findIndex(g => g.id === group.id)
  );

  console.log(`Verifying ${uniqueMissingGroups.length} unique missing groups...`);
  console.log(`Getting ALL inventory_billable_groups for comparison...`);
  console.log(`=============================================\n`);

  // Get all groups to compare against
  databaseConnection.obj.getAll('inventory_billable_groups', function(allGroups) {
    console.log(`Retrieved ${allGroups.length} total inventory_billable_groups from database.`);

    // Process each missing group
    uniqueMissingGroups.forEach((missingGroup, index) => {
      console.log(`\n--- Verifying missing group ${index + 1}/${uniqueMissingGroups.length} ---`);
      console.log(`Looking for: "${missingGroup.name}" (ID: ${missingGroup.id})`);
      console.log(`Expected copy name: "${missingGroup.name} (*)"`);

      // Look for exact match
      const exactMatches = allGroups.filter(g => g.name === missingGroup.name + ' (*)');
      console.log(`Exact matches found: ${exactMatches.length}`);

      if (exactMatches.length > 0) {
        exactMatches.forEach((match, i) => {
          console.log(`  Exact match ${i + 1}: "${match.name}" (ID: ${match.id})`);
        });
      }

      // Look for partial matches (contains the original name)
      const partialMatches = allGroups.filter(g =>
        g.name.includes(missingGroup.name) && g.name.includes('(*)')
      );
      console.log(`Partial matches found: ${partialMatches.length}`);

      if (partialMatches.length > 0) {
        partialMatches.forEach((match, i) => {
          console.log(`  Partial match ${i + 1}: "${match.name}" (ID: ${match.id})`);
        });
      }

      // Look for similar names (case insensitive, trimmed)
      const similarMatches = allGroups.filter(g => {
        const cleanOriginal = missingGroup.name.trim().toLowerCase();
        const cleanTarget = g.name.replace(' (*)', '').trim().toLowerCase();
        return cleanTarget === cleanOriginal && g.name.includes('(*)');
      });
      console.log(`Similar matches (case/space insensitive): ${similarMatches.length}`);

      if (similarMatches.length > 0) {
        similarMatches.forEach((match, i) => {
          console.log(`  Similar match ${i + 1}: "${match.name}" (ID: ${match.id})`);
          console.log(`    Original chars: [${missingGroup.name.split('').map(c => c.charCodeAt(0)).join(', ')}]`);
          console.log(`    Match chars: [${match.name.replace(' (*)', '').split('').map(c => c.charCodeAt(0)).join(', ')}]`);
        });
      }

      if (exactMatches.length === 0 && partialMatches.length === 0 && similarMatches.length === 0) {
        console.log(`  ❌ NO MATCHES FOUND - This group truly needs to be created`);
      } else {
        console.log(`  ⚠️ MATCHES FOUND - Check why our query didn't find these!`);
      }
    });

    console.log(`\n========== VERIFICATION COMPLETE ==========`);
    console.log(`Review the matches above to see if groups exist but our query logic is failing.`);
  });
}

/**
 * SEPARATE FUNCTION: Create missing groups from the collected global list
 * @param {boolean} dryRun - If true, only logs what would be created
 */
function createMissingGroupsFromList(dryRun = true) {
  console.log(`\n========== CREATING MISSING GROUPS FROM LIST ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will create records)'}`);

  if (globalMissingGroups.length === 0) {
    console.log(`❌ No missing groups in global list. Run updateCombinationGroupReferences() first.`);
    return;
  }

  // Remove duplicates based on ID
  const uniqueMissingGroups = globalMissingGroups.filter((group, index, self) =>
    index === self.findIndex(g => g.id === group.id)
  );

  console.log(`Creating ${uniqueMissingGroups.length} unique missing groups...`);
  console.log(`======================================================\n`);

  // Process groups sequentially
  processGroupCreation(uniqueMissingGroups, 0, dryRun);
}

/**
 * Process group creation one at a time
 */
function processGroupCreation(groups, groupIndex, dryRun) {
  if (groupIndex >= groups.length) {
    console.log(`\n========== GROUP CREATION COMPLETE ==========`);
    console.log(`Created ${groups.length} groups.`);
    console.log(`Now you can re-run updateCombinationGroupReferences() to update the combinations.`);
    return;
  }

  const originalGroup = groups[groupIndex];
  console.log(`\n--- Creating group ${groupIndex + 1}/${groups.length} ---`);
  console.log(`Original: "${originalGroup.name}" (ID: ${originalGroup.id})`);

  // Clone the group for duplication (based on original script logic)
  var newGroup = _.clone(originalGroup);

  // Remove the id property as a new one will be assigned by the database
  delete newGroup.id;

  // Update name by appending "(*)"
  newGroup.name = originalGroup.name + ' (*)';

  // Remove surcharges with ID 1310026 if it exists (from original script)
  if (newGroup.surcharges && Array.isArray(newGroup.surcharges)) {
    const originalLength = newGroup.surcharges.length;
    newGroup.surcharges = newGroup.surcharges.filter(id => id !== 1310026);
    console.log(`Modified surcharges: ${originalLength} → ${newGroup.surcharges.length} (Removed 1310026)`);
  } else {
    console.log(`No surcharges to modify`);
  }

  // Update date_created to current time
  newGroup.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new group: "${newGroup.name}"...`);
    databaseConnection.obj.create('inventory_billable_groups', newGroup, function(createdGroup) {
      console.log(`✅ CREATED: "${createdGroup.name}" (ID: ${createdGroup.id})`);

      // Continue with next group after a short delay
      setTimeout(function() {
        processGroupCreation(groups, groupIndex + 1, dryRun);
      }, 100);
    });
  } else {
    console.log(`[DRY RUN] Would create: "${newGroup.name}"`);
    console.log(`  Category: ${newGroup.category}`);
    console.log(`  Description: "${newGroup.description || 'No description'}"`);

    // Continue with next group (no delay needed for dry run)
    processGroupCreation(groups, groupIndex + 1, dryRun);
  }
}

/**
 * Update the combination with new group IDs based on the mapping
 */
function updateCombinationWithNewGroupIds(fullCombo, groupMapping, combinations, comboIndex, categoryId, limit, startIndex) {
  console.log(`\n--- Updating combination "${fullCombo.name}" with new group IDs ---`);

  if (Object.keys(groupMapping).length === 0) {
    console.log(`No group mappings found, nothing to update.`);

    // Process next combination
    processCombinationDetails(combinations, comboIndex + 1, categoryId, limit, startIndex);
    return;
  }

  // Clone the combination for modification
  var updatedCombination = _.clone(fullCombo);
  var hasChanges = false;
  var changesLog = [];

  // Update items array
  if (updatedCombination.items && Array.isArray(updatedCombination.items)) {
    updatedCombination.items.forEach((item, itemIndex) => {
      // Update direct inventory_group reference
      if (item.inventory_group && groupMapping[item.inventory_group]) {
        const oldId = item.inventory_group;
        const newId = groupMapping[item.inventory_group];
        item.inventory_group = newId;
        hasChanges = true;
        changesLog.push(`items[${itemIndex}].inventory_group: ${oldId} → ${newId}`);
        console.log(`  ✅ UPDATED: items[${itemIndex}].inventory_group: ${oldId} → ${newId}`);
      }

      // Update choices array inventory_group references
      if (item.choices && Array.isArray(item.choices)) {
        item.choices.forEach((choice, choiceIndex) => {
          if (choice.inventory_group && groupMapping[choice.inventory_group]) {
            const oldId = choice.inventory_group;
            const newId = groupMapping[choice.inventory_group];
            choice.inventory_group = newId;
            hasChanges = true;
            changesLog.push(`items[${itemIndex}].choices[${choiceIndex}].inventory_group: ${oldId} → ${newId}`);
            console.log(`  ✅ UPDATED: items[${itemIndex}].choices[${choiceIndex}].inventory_group: ${oldId} → ${newId}`);
          }
        });
      }
    });
  }

  console.log(`\nSUMMARY: ${hasChanges ? 'Changes made' : 'No changes needed'}`);
  if (hasChanges) {
    console.log(`Changes made:`);
    changesLog.forEach((change, index) => {
      console.log(`  ${index + 1}. ${change}`);
    });

    console.log(`\n[DRY RUN] Would update combination "${fullCombo.name}" (ID: ${fullCombo.id})`);

    // Show verification of what the updated groups would be
    console.log(`\n--- VERIFICATION: Showing updated group details ---`);
    verifyUpdatedGroups(updatedCombination, 0, combinations, comboIndex, categoryId, limit, startIndex);
  } else {
    console.log(`No changes needed for this combination.`);

    // Process next combination
    processCombinationDetails(combinations, comboIndex + 1, categoryId, limit, startIndex);
  }
}

/**
 * Verify the updated groups by querying their details
 */
function verifyUpdatedGroups(updatedCombination, itemIndex, combinations, comboIndex, categoryId, limit, startIndex) {
  if (!updatedCombination.items || itemIndex >= updatedCombination.items.length) {
    console.log(`\n--- Verification complete ---`);

    // Process next combination
    processCombinationDetails(combinations, comboIndex + 1, categoryId, limit, startIndex);
    return;
  }

  const item = updatedCombination.items[itemIndex];
  console.log(`\nVerifying Item ${itemIndex + 1}:`);

  if (item.inventory_group && item.inventory_group !== 0) {
    databaseConnection.obj.getById('inventory_billable_groups', item.inventory_group, function(group) {
      console.log(`  Direct inventory_group after update:`);
      console.log(`    ID: ${group.id}`);
      console.log(`    Name: "${group.name}"`);
      console.log(`    Has "(*)" in name: ${group.name.includes('(*)')}`);

      // Check choices if they exist
      if (item.choices && Array.isArray(item.choices) && item.choices.length > 0) {
        console.log(`  Checking ${item.choices.length} choices...`);
        verifyChoiceGroups(item.choices, 0, itemIndex, function() {
          // Continue with next item
          verifyUpdatedGroups(updatedCombination, itemIndex + 1, combinations, comboIndex, categoryId, limit, startIndex);
        });
      } else {
        // Continue with next item
        verifyUpdatedGroups(updatedCombination, itemIndex + 1, combinations, comboIndex, categoryId, limit, startIndex);
      }
    });
  } else {
    console.log(`  No direct inventory_group to verify`);

    // Check choices if they exist
    if (item.choices && Array.isArray(item.choices) && item.choices.length > 0) {
      console.log(`  Checking ${item.choices.length} choices...`);
      verifyChoiceGroups(item.choices, 0, itemIndex, function() {
        // Continue with next item
        verifyUpdatedGroups(updatedCombination, itemIndex + 1, combinations, comboIndex, categoryId, limit, startIndex);
      });
    } else {
      // Continue with next item
      verifyUpdatedGroups(updatedCombination, itemIndex + 1, combinations, comboIndex, categoryId, limit, startIndex);
    }
  }
}

/**
 * Verify choice groups
 */
function verifyChoiceGroups(choices, choiceIndex, itemIndex, callback) {
  if (choiceIndex >= choices.length) {
    if (callback) callback();
    return;
  }

  const choice = choices[choiceIndex];
  if (choice.inventory_group && choice.inventory_group !== 0) {
    databaseConnection.obj.getById('inventory_billable_groups', choice.inventory_group, function(group) {
      console.log(`    Choice ${choiceIndex + 1} inventory_group after update:`);
      console.log(`      ID: ${group.id}`);
      console.log(`      Name: "${group.name}"`);
      console.log(`      Has "(*)" in name: ${group.name.includes('(*)')}`);

      // Continue with next choice
      verifyChoiceGroups(choices, choiceIndex + 1, itemIndex, callback);
    });
  } else {
    // Continue with next choice
    verifyChoiceGroups(choices, choiceIndex + 1, itemIndex, callback);
  }
}

/**
 * Process combinations in the target category
 */
function processCombinations(groupMapping, dryRun, limit, startIndex, categoryId) {
  console.log(`\nStep 2: Processing combinations in category ${categoryId}...`);

  // Query combinations using the same structure as the XHR
  const queryObj = {
    category: categoryId,
    paged: {
      page: 0,
      pageLength: 50,
      paged: true,
      sortCol: "name",
      sortDir: "asc",
      sortCast: "string",
      count: true
    }
  };

  databaseConnection.obj.getWhere('inventory_billable_combinations', queryObj, function(combinations) {
    console.log(`Found ${combinations.length} combinations in category ${categoryId}.`);

    // Filter to only combinations with "(*)" in name
    const copyCombinations = combinations.filter(combo => combo.name.includes('(*)'));
    console.log(`Found ${copyCombinations.length} combinations with "(*)" in their names.`);

    if (copyCombinations.length === 0) {
      console.warn(`⚠️ WARNING: No combinations with "(*)" found in category ${categoryId}.`);
      return;
    }

    // Apply start index and limit
    const combinationsToProcess = copyCombinations.slice(startIndex, startIndex + limit);

    if (combinationsToProcess.length === 0) {
      console.warn(`⚠️ WARNING: No combinations to process with current settings. Check your startIndex (${startIndex}) and limit (${limit}).`);
      return;
    }

    console.log(`\nWill process ${combinationsToProcess.length} combinations:`);
    combinationsToProcess.forEach((combo, idx) => {
      console.log(`  ${idx+1}. "${combo.name}" (ID: ${combo.id})`);
    });

    // Track statistics
    const stats = {
      combinationsProcessed: 0,
      combinationsUpdated: 0,
      combinationsSkipped: 0,
      groupReferencesFound: 0,
      groupReferencesUpdated: 0,
      groupReferencesNotMapped: 0
    };

    // Process combinations sequentially
    processNextCombinationUpdate(combinationsToProcess, 0, groupMapping, dryRun, stats);
  });
}

/**
 * Process combinations one at a time to avoid server connection issues
 */
function processNextCombinationUpdate(combinations, index, groupMapping, dryRun, stats) {
  if (index >= combinations.length) {
    console.log(`\n========== GROUP REFERENCE UPDATE PROCESS COMPLETE ==========`);
    console.log(`Combinations processed: ${stats.combinationsProcessed}`);
    console.log(`Combinations updated: ${stats.combinationsUpdated}`);
    console.log(`Combinations skipped (no changes needed): ${stats.combinationsSkipped}`);
    console.log(`Group references found: ${stats.groupReferencesFound}`);
    console.log(`Group references updated: ${stats.groupReferencesUpdated}`);
    console.log(`Group references not mapped: ${stats.groupReferencesNotMapped}`);
    console.log(`==============================================================\n`);
    return;
  }

  const combination = combinations[index];
  stats.combinationsProcessed++;

  console.log(`\n========== COMBINATION ${index+1}/${combinations.length} ==========`);
  console.log(`Processing combination: "${combination.name}" (ID: ${combination.id})`);

  // Verify this combination belongs to a category with "(*)"
  if (!combination.name.includes('(*)')) {
    console.warn(`⚠️ SKIPPING: Combination "${combination.name}" does not have "(*)" in name`);
    stats.combinationsSkipped++;
    processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
    return;
  }

  // Clone the combination for modification
  var updatedCombination = _.clone(combination);
  var hasChanges = false;

  // Process items array
  if (updatedCombination.items && Array.isArray(updatedCombination.items)) {
    console.log(`Found ${updatedCombination.items.length} items in combination.`);

    updatedCombination.items.forEach((item, itemIndex) => {
      console.log(`  Processing item ${itemIndex + 1}: "${item.name || 'Unnamed item'}"`);

      // Process choices array within each item
      if (item.choices && Array.isArray(item.choices)) {
        console.log(`    Found ${item.choices.length} choices in item ${itemIndex + 1}.`);

        item.choices.forEach((choice, choiceIndex) => {
          stats.groupReferencesFound++;

          if (choice.inventory_group && choice.inventory_group !== 0) {
            const originalGroupId = choice.inventory_group;
            console.log(`      Choice ${choiceIndex + 1}: inventory_group = ${originalGroupId}`);

            if (groupMapping[originalGroupId]) {
              const newGroupId = groupMapping[originalGroupId];
              choice.inventory_group = newGroupId;
              hasChanges = true;
              stats.groupReferencesUpdated++;
              console.log(`        ✅ UPDATED: ${originalGroupId} → ${newGroupId}`);
            } else {
              stats.groupReferencesNotMapped++;
              console.log(`        ⚠️ NOT MAPPED: Group ID ${originalGroupId} not found in mapping`);
            }
          } else {
            console.log(`      Choice ${choiceIndex + 1}: no inventory_group or inventory_group = 0`);
          }
        });
      } else {
        console.log(`    No choices array found in item ${itemIndex + 1}.`);
      }
    });
  } else {
    console.log(`No items array found in combination.`);
  }

  // Update the combination if changes were made
  if (hasChanges) {
    if (!dryRun) {
      console.log(`Updating combination "${combination.name}"...`);

      databaseConnection.obj.update('inventory_billable_combinations', updatedCombination, function() {
        stats.combinationsUpdated++;
        console.log(`✅ UPDATED: Combination "${combination.name}" (ID: ${combination.id})`);

        // Process the next combination after a short delay
        setTimeout(function() {
          processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
        }, 100);
      });
    } else {
      console.log(`[DRY RUN] Would update combination "${combination.name}" with ${stats.groupReferencesUpdated} group reference changes.`);
      stats.combinationsUpdated++;

      // Process the next combination (no delay needed for dry run)
      processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
    }
  } else {
    console.log(`No changes needed for combination "${combination.name}".`);
    stats.combinationsSkipped++;

    // Process the next combination (no delay needed)
    processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
  }
}
