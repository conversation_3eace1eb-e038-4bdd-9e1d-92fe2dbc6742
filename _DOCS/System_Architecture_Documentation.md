# Bento Framework Inventory System Architecture

## Overview

The Bento framework implements a complex three-tier inventory management system for catering/event management. This document details the architecture discovered during the BEO choice items investigation.

---

## Core Object Types

### 1. `inventory_billable_combination_categories`
**Purpose**: Top-level categorization containers
**Database Table**: `inventory_billable_combination_categories`

**Structure**:
```javascript
{
  "id": ********,
  "name": "Food - 2025 Pricing (*)",
  "object_bp_type": "inventory_billable_combination_categories",
  "chart_of_account": 1122843,
  "default_pricing_option": "price_per_person"
}
```

**Key Properties**:
- `id`: Unique identifier
- `name`: Display name (may include (*) suffix for duplicates)
- `chart_of_account`: Financial account linkage
- `default_pricing_option`: Pricing methodology

### 2. `inventory_billable_combinations`
**Purpose**: Main menu items/packages
**Database Table**: `inventory_billable_combinations`

**Structure**:
```javascript
{
  "id": ********,
  "name": "Buffet Entree* (*)",
  "category": ********,
  "items": [
    {
      "id": 1,
      "inventory_group": ********,
      "qty": { "quantity": 1, "unit_type": "servings" },
      "choices": []
    }
  ],
  "price_per_person": 5800
}
```

**Key Properties**:
- `category`: References `inventory_billable_combination_categories.id`
- `items[]`: Array of component references
- `items[].inventory_group`: References `inventory_billable_groups.id`
- `price_per_person`: Pricing in cents

### 3. `inventory_billable_groups`
**Purpose**: Component building blocks and choice lists
**Database Table**: `inventory_billable_groups`

**Structure**:
```javascript
{
  "id": ********,
  "name": "Salad Choice List (*)",
  "category": ********,
  "items": [
    {
      "id": 1,
      "name": "Salad Choice Option",
      "max_selections": 1,
      "choices": [
        {
          "id": 13,
          "inventory_group": 6225382,
          "additional_price": 0
        }
      ]
    }
  ]
}
```

**Key Properties**:
- `category`: References group category (different from combination category)
- `items[]`: Array of selectable items
- `items[].choices[]`: Nested choice options
- `items[].choices[].inventory_group`: Deep nested references

---

## Data Relationships

### Hierarchy Flow
```
Category (********: "Food - 2025 Pricing (*)")
└── Combination (********: "Buffet Entree* (*)")
    └── items[0].inventory_group (********: "Assorted Breads & Rolls (*)")
        └── items[0].inventory_group (20435098: "Flavored Butter (*)")
            └── items[0].choices[0].inventory_group (1751621: "Chimichurri Butter")
```

### Reference Types
1. **Category → Combination**: `combinations.category → categories.id`
2. **Combination → Group**: `combinations.items[].inventory_group → groups.id`
3. **Group → Group**: `groups.items[].inventory_group → groups.id` (nested)
4. **Choice → Group**: `groups.items[].choices[].inventory_group → groups.id` (deep nested)

---

## Category System

### Combination Categories vs. Group Categories
**Critical Discovery**: Combinations and Groups use DIFFERENT category systems

#### Combination Categories
- **Purpose**: Organize menu packages
- **Example**: `********` ("Food - 2025 Pricing (*)")
- **Used by**: BEO merge tags for filtering

#### Group Categories  
- **Purpose**: Organize component pieces
- **Example**: `********` (duplicated group category)
- **Used by**: Internal group organization

### Category Relationships
```
Combination Category: ******** ("Food - 2025 Pricing (*)")
├── Contains combinations that reference groups in...
└── Group Category: ******** (duplicated groups)
    └── Contains the actual component groups
```

---

## Duplication System

### Naming Convention
**Pattern**: Original items duplicated with " (*)" suffix
- Original: "Buffet Entree"
- Duplicated: "Buffet Entree (*)"

### Reference Mapping
During duplication, references should be updated:
```javascript
// Original structure
combination.items[0].inventory_group = 1751612; // "Assorted Breads & Rolls"

// After duplication (corrected)
combination.items[0].inventory_group = ********; // "Assorted Breads & Rolls (*)"
```

### Data Source Tracking
**Property**: `data_source`
**Purpose**: Track relationship between original and duplicate
**Issue**: Not consistently implemented in current duplication script

---

## BEO Merge Tag System

### Function Location
**File**: `public_html/application/libraries/bento_merge_tags.php`
**Function**: `generateInfinityBEOMergeTag()`

### Processing Flow
1. **Category Filtering**: Filter combinations by category ID
2. **Menu Item Resolution**: Load selected menu items
3. **Choice Resolution**: Resolve nested choice structures
4. **HTML Generation**: Build hierarchical HTML output

### Expected Output Structure
```html
<b>Buffet Entree* (*)</b>
<ul>
  <li><b>Assorted Breads & Rolls</b></li>
  <ul>
    <li><b>Flavored Butter</b></li>
    <ul>
      <li><b>Chimichurri Butter</b></li>
    </ul>
  </ul>
  <li><b>Salad Choice List</b></li>
  <ul>
    <li><b>Balsamic Grape & Apple Salad</b></li>
    <ul>
      <li><b>Dressing</b></li>
      <ul>
        <li><b>Bleu Cheese Dressing</b></li>
        <li><b>Pre-Dressed</b></li>
      </ul>
    </ul>
  </ul>
</ul>
```

---

## Technical Debt Issues

### 1. Shallow Cloning
**File**: `_DOCS/Developer Workflows/scripts/copy_categories_and_groups.js`
**Issue**: Uses `_.clone()` instead of deep cloning
**Impact**: Complex nested objects copied by reference

### 2. Reference Integrity
**Problem**: No database constraints on inventory_group references
**Risk**: Broken references can exist without detection

### 3. Multi-Category Complexity
**Issue**: Combinations and groups use different category systems
**Confusion**: Makes relationship tracking difficult

### 4. Deep Nesting Complexity
**Levels**: Up to 4+ levels of nesting in choice structures
**Challenge**: Reference updates must traverse entire tree

---

## Data Integrity Patterns

### Verification Queries
```javascript
// Check combination references
databaseConnection.obj.getById('inventory_billable_combinations', ID, callback);

// Check group choice counts
databaseConnection.obj.getById('inventory_billable_groups', ID, function(group) {
  console.log(`Choices: ${group.items?.[0]?.choices?.length || 0}`);
});

// Find duplicated items
databaseConnection.obj.getWhere('inventory_billable_groups', {
  name: { type: 'contains', value: '(*)' }
}, callback);
```

### Reference Validation
```javascript
// Validate reference chain
function validateReferenceChain(combinationId) {
  // 1. Load combination
  // 2. Check each items[].inventory_group exists
  // 3. Check each referenced group has items
  // 4. Check nested choices[] references
}
```

---

## Performance Considerations

### Query Patterns
- **BEO Generation**: Requires deep traversal of nested structures
- **Reference Resolution**: Multiple database lookups per menu item
- **Category Filtering**: Large result sets need efficient filtering

### Optimization Opportunities
1. **Eager Loading**: Load related objects in single queries
2. **Caching**: Cache resolved choice structures
3. **Indexing**: Ensure proper database indexes on reference fields

---

## Security Considerations

### Data Access
- **User Permissions**: Check read/write permissions on inventory objects
- **Instance Isolation**: Ensure proper instance-based filtering
- **Category Access**: Verify user access to specific categories

### Reference Validation
- **Existence Checks**: Validate referenced objects exist
- **Permission Checks**: Ensure user can access referenced objects
- **Circular Reference Prevention**: Detect and prevent circular references

---

*Documentation compiled from investigation: July 11, 2025*
*Based on: Live system analysis and database structure examination*
*Status: Architecture documented, BEO issue unresolved*
