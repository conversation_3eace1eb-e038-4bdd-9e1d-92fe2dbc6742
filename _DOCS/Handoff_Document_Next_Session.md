# BEO Choice Items Debug - Handoff Document

## Current Situation

**CRITICAL**: Despite comprehensive database fixes, BEO choice items still not rendering. All reference integrity verified at database level, but issue persists.

---

## Immediate Context

### Problem Statement
- **Working**: Original categories show full nested choice structure in BEO merge tags
- **Broken**: Duplicated categories (*) show only main items, missing all choice details
- **Verified**: Database references are 100% correct after our fixes

### Last Actions Taken
1. ✅ **Fixed combination references** - 30 combinations updated to point to (*) groups
2. ✅ **Fixed deep nested references** - 1,257 groups processed, hundreds of nested inventory_group references updated
3. ✅ **Verified data integrity** - Database queries confirm all references correct
4. ❌ **BEO still broken** - Choice items not appearing despite fixes

---

## Key Technical Details

### Working Data Structure (Verified)
```javascript
// Combination: "Buffet Entree* (*)" (ID: 20456859)
items: [
  { inventory_group: 20435097 }, // ✓ Assorted Breads & Rolls (*)
  { inventory_group: 20434616 }, // ✓ Salad Serving Style (*)  
  { inventory_group: 20434630 }, // ✓ Salad Choice List (*)
  { inventory_group: 20434495 }, // ✓ Buffet Entree Choice List - 1 Selection (*)
  { inventory_group: 20434052 }  // ✓ Sides Choice List - 3 Selections (*)
]

// Referenced groups have choices:
// - Salad Choice List (*): 19 choices
// - Buffet Entree Choice List (*): 30 choices  
// - Sides Choice List (*): 90 choices
```

### Categories
- **Duplicated Category**: `20456830` ("Food - 2025 Pricing (*)")
- **Original Category**: `1162747` (working)
- **BEO Function**: `generateInfinityBEOMergeTag()` in `bento_merge_tags.php`

---

## Investigation Priorities for Next Session

### 1. BEO Merge Tag Function Analysis (HIGHEST PRIORITY)
**File**: `public_html/application/libraries/bento_merge_tags.php`
**Function**: `generateInfinityBEOMergeTag()` (lines ~4888-4930)

**Key Questions**:
- How does it filter/query for inventory groups?
- Does it use category filtering that might exclude our (*) groups?
- Are there hardcoded category IDs or filters?
- Does it check for specific naming patterns?

### 2. Cache Investigation
**Potential Issues**:
- Application-level caching of BEO output
- Database query result caching
- Browser/client-side caching
- Merge tag processing cache

**Actions Needed**:
- Clear all caches
- Check for cache configuration
- Test with cache disabled

### 3. Selection State Logic
**Critical Question**: How does BEO determine which choices are "selected"?

**Investigation Points**:
- Does it require specific choice selection data?
- Are there menu item selection requirements?
- Does it need `choices[]` array populated in menu line items?
- Are there state flags or selection indicators needed?

### 4. Category Filtering Deep Dive
**Hypothesis**: BEO merge tag may be filtering out (*) groups due to category mismatch

**Check**:
- What categories are the (*) groups assigned to?
- Does BEO filter by specific category IDs?
- Are there category inclusion/exclusion rules?

---

## Debugging Scripts Ready to Use

### 1. Quick Data Verification
```javascript
// Verify combination references
databaseConnection.obj.getById('inventory_billable_combinations', 20456859, function(combo) {
  console.log('Combination references:');
  combo.items.forEach((item, i) => {
    console.log(`  Item ${i+1}: ${item.inventory_group}`);
  });
});
```

### 2. Group Choice Verification  
```javascript
// Check if referenced groups have choices
[20434630, 20434495, 20434052].forEach(groupId => {
  databaseConnection.obj.getById('inventory_billable_groups', groupId, function(group) {
    console.log(`${group.name}: ${group.items?.[0]?.choices?.length || 0} choices`);
  });
});
```

### 3. Category Analysis
```javascript
// Check categories of (*) groups
databaseConnection.obj.getWhere('inventory_billable_groups', {
  name: { type: 'contains', value: '(*)' }
}, function(groups) {
  const categories = {};
  groups.forEach(g => {
    categories[g.category] = (categories[g.category] || 0) + 1;
  });
  console.log('(*) groups by category:', categories);
});
```

---

## Critical Files to Examine

### 1. BEO Merge Tag Implementation
- **File**: `public_html/application/libraries/bento_merge_tags.php`
- **Function**: `generateInfinityBEOMergeTag()`
- **Focus**: Category filtering, choice resolution logic

### 2. Menu Processing Logic
- **Look for**: How menu items are processed
- **Check**: Selection state requirements
- **Verify**: Choice item resolution algorithm

### 3. Database Access Patterns
- **Investigate**: How BEO queries for related data
- **Check**: Join conditions, filtering logic
- **Verify**: Category-based filtering

---

## Test Cases to Run

### 1. Minimal Test Case
Create a simple menu with one combination from (*) category and trace complete data flow

### 2. Comparison Test
Run identical BEO merge tag on:
- Original category (working)
- Duplicated category (broken)
Compare the processing differences

### 3. Direct Function Test
Call `generateInfinityBEOMergeTag()` directly with specific category IDs and debug output

---

## Hypotheses to Test

### 1. Category Filtering Issue
**Theory**: BEO merge tag filters by category and excludes (*) groups
**Test**: Check category assignments of all (*) groups

### 2. Selection State Requirement
**Theory**: BEO requires specific selection state data that's missing
**Test**: Compare selection data between working and broken examples

### 3. Caching Issue
**Theory**: Stale cached data preventing updated references from being used
**Test**: Clear all caches and retest

### 4. Naming Pattern Issue
**Theory**: BEO has hardcoded logic that excludes (*) suffixed items
**Test**: Check for string matching or filtering logic in BEO function

---

## Success Criteria

**Goal**: BEO merge tag for duplicated category shows complete nested choice structure identical to original category

**Verification**: 
```
Buffet Entree* (*)
├── Assorted Breads & Rolls
│   └── Flavored Butter (with choice options)
├── Salad Choice List  
│   └── Multiple salad options with dressing choices
├── Buffet Entree Choice List
│   └── 30 entree options with ingredients
└── Sides Choice List
    └── 90 side options
```

---

## Next Session Action Plan

1. **Start with BEO function analysis** - examine `generateInfinityBEOMergeTag()` code
2. **Test category filtering hypothesis** - check if (*) groups are being filtered out
3. **Investigate selection state requirements** - compare working vs broken selection data
4. **Clear all caches** - eliminate caching as potential cause
5. **Create minimal test case** - isolate the exact failure point

---

*Handoff prepared: July 11, 2025*
*Status: Database integrity verified, BEO rendering still broken*
*Priority: Investigate BEO merge tag function logic*
