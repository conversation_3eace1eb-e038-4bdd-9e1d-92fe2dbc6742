# BEO Choice Items Investigation - Technical Analysis

## Executive Summary

**Problem**: <PERSON>EO (Banquet Event Order) merge tags in the Infinity instance are displaying main menu items but failing to render nested choice item details for newly duplicated categories.

**Root Cause**: Complex multi-level data structure corruption during category duplication process, involving shallow cloning issues and broken reference chains across three distinct object types.

**Status**: Multiple fixes applied, issue persists - requires deeper architectural analysis.

---

## System Architecture Overview

### Data Structure Hierarchy

The Bento framework uses a three-tier inventory system:

```
1. inventory_billable_combination_categories (Top Level)
   └── Contains: inventory_billable_combinations (Menu Items)
       └── Contains: items[] array referencing inventory_billable_groups (Components)
           └── Contains: items[] array with nested choices[] arrays
               └── Contains: inventory_group references (Deep Nesting)
```

### Key Object Types

#### 1. `inventory_billable_combination_categories`
- **Purpose**: Top-level categorization (e.g., "Food - 2025 Pricing (*)")
- **Category ID**: `20456830` (duplicated category)
- **Original Category**: `1162747` (working category)

#### 2. `inventory_billable_combinations` 
- **Purpose**: Main menu items (e.g., "Buffet Entree* (*)", "Stations - Up to three (3) selections...")
- **Structure**: Contains `items[]` array with `inventory_group` references
- **Count**: 30 combinations in duplicated category
- **Example**: "Plated Entrée - Single Entrée Section (A) (*)" (ID: 20456832)

#### 3. `inventory_billable_groups`
- **Purpose**: Component building blocks and choice lists
- **Structure**: Contains `items[]` array with nested `choices[]` arrays
- **Deep Nesting**: `items[].choices[].inventory_group` references
- **Count**: 4,051 duplicated groups with (*) suffix

---

## Problem Analysis

### Working vs. Broken Comparison

#### Working Example (Original Categories)
```
Entree 3
├── Stations - Up to three (3) selections from section A, plus one (1) from section B
    ├── A Stations Choice List - 3 Selections
    │   ├── Grilled Flatbreads (choose 3)
    │   │   ├── ✓ Cheddar-Apple Flatbread (aged white cheddar | honey crisp apples...)
    │   │   ├── ✓ Roasted Fig & Goat Cheese Grilled Flatbread (arugula | balsamic reduction...)
    │   │   └── ✓ Hot Chicken Flatbread (ranch | hot chicken bites...)
    │   ├── Macaroni & Cheese (Choose 2)
    │   └── Beef Sliders (Choose 2)
    └── B Stations Choice List - 1 Selection
        └── Tennessee Food Truck (5 items with ingredients)
```

#### Broken Example (Duplicated Categories)
```
Buffet Entree* (*)
├── Main item name displays ✓
└── Choice items missing ❌ (should show salads, entrees, sides with details)
```

### Technical Root Causes Identified

#### 1. Shallow Cloning in Original Duplication Script
**File**: `_DOCS/Developer Workflows/scripts/copy_categories_and_groups.js`
**Issue**: Line uses `_.clone(ibg)` instead of deep clone
```javascript
// PROBLEMATIC CODE
var newIBG = _.clone(ibg);  // Shallow clone only
```
**Impact**: Complex nested objects (`items[]` arrays) copied by reference, not value

#### 2. Reference Chain Corruption
**Problem**: Duplicated combinations reference original groups instead of duplicated groups
**Evidence**: 
- Combination "Buffet Entree* (*)" (ID: 20456859) initially had items referencing original groups
- Fixed via `updateCombinationsToReferenceDuplicatedGroups()` script

#### 3. Deep Nesting Reference Issues
**Problem**: Multi-level `inventory_group` references not updated during duplication
**Structure**:
```javascript
items: [
  {
    inventory_group: 1779519,  // Should reference (*) version
    choices: [
      {
        inventory_group: 1751616,  // Nested reference also needs updating
      }
    ]
  }
]
```

---

## Investigation Timeline & Fixes Applied

### Phase 1: Initial Diagnosis
1. **Identified working vs. broken examples**
2. **Discovered two-tier system**: combinations → groups
3. **Found reference mismatches** in combinations

### Phase 2: Combination Reference Fixes
**Script**: `updateCombinationsToReferenceDuplicatedGroups()`
- **Scope**: Updated 30 combinations in category 20456830
- **Result**: All top-level `inventory_group` references corrected
- **Verification**: Database queries confirmed updates applied

### Phase 3: Deep Nesting Fixes
**Script**: `deepUpdateInventoryGroupReferences()`
- **Scope**: 1,257 duplicated groups processed
- **Updates**: Hundreds of nested `items[].choices[].inventory_group` references
- **Result**: Comprehensive deep reference correction

### Phase 4: Verification Attempts
**Tests Performed**:
1. Database queries confirming reference updates
2. BEO merge tag re-testing
3. Manual data structure inspection

---

## Current Status

### What's Working
- ✅ Main menu items display correctly
- ✅ Database references are correctly updated
- ✅ Combination → Group references point to (*) versions
- ✅ Deep nested references updated to (*) versions

### What's Still Broken
- ❌ Choice item details not rendering in BEO output
- ❌ Nested choice structure not appearing

### Verified Data Integrity
**Combination "Buffet Entree* (*)" (ID: 20456859)**:
```javascript
items: [
  { inventory_group: 20435097 }, // ✓ Assorted Breads & Rolls (*)
  { inventory_group: 20434616 }, // ✓ Salad Serving Style (*)
  { inventory_group: 20434630 }, // ✓ Salad Choice List (*)
  { inventory_group: 20434495 }, // ✓ Buffet Entree Choice List - 1 Selection (*)
  { inventory_group: 20434052 }  // ✓ Sides Choice List - 3 Selections (*)
]
```

**Referenced Groups Have Items**:
- "Salad Choice List (*)" (ID: 20434630): 19 choices ✓
- "Buffet Entree Choice List - 1 Selection (*)" (ID: 20434495): 30 choices ✓
- "Sides Choice List - 3 Selections (*)" (ID: 20434052): 90 choices ✓

---

## BEO Merge Tag Analysis

### Function Location
**File**: `public_html/application/libraries/bento_merge_tags.php`
**Function**: `generateInfinityBEOMergeTag()` (lines ~4888-4930)

### Processing Logic
1. **Category Filtering**: Filters combinations by category IDs
2. **Menu Item Processing**: Iterates through selected menu items
3. **Choice Resolution**: Resolves choice items from referenced groups
4. **HTML Generation**: Builds nested HTML structure

### Suspected Issues
1. **Category Filtering Logic**: May not properly include referenced groups
2. **Choice Resolution Algorithm**: May fail on updated reference structure
3. **Caching Issues**: Possible stale data in merge tag processing
4. **Selection State Logic**: May not properly detect made choices

---

## Technical Debt & Architectural Issues

### 1. Shallow Cloning Pattern
**Impact**: Systematic issue affecting all duplication operations
**Recommendation**: Implement deep cloning utility function

### 2. Reference Integrity
**Issue**: No referential integrity constraints in database
**Impact**: Broken references can exist without detection

### 3. Multi-Level Dependencies
**Complexity**: Three-tier dependency chain creates fragile architecture
**Risk**: Changes at any level can break downstream functionality

---

## Data Samples

### Working Group Structure
```javascript
// "Dyer" (ID: 5347743) - Working original group
{
  "name": "Dyer",
  "items": [
    {
      "id": 1,
      "name": "Choose which day & time you need this venue",
      "max_selections": 1,
      "choices": [
        {
          "id": 11,
          "name": "Monday - Friday - Morning",
          "additional_price": 400000
        }
        // ... 8 more choices
      ]
    }
  ]
}
```

### Problematic Group Structure
```javascript
// "Assorted Breads & Rolls (*)" (ID: 20435097) - After fixes
{
  "name": "Assorted Breads & Rolls (*)",
  "items": [
    {
      "id": 1,
      "inventory_group": 20435098, // ✓ Now points to "Flavored Butter (*)"
      "choices": []
    }
  ]
}
```

---

## Next Steps Required

### 1. BEO Merge Tag Deep Dive
- **Analyze**: `generateInfinityBEOMergeTag()` function logic
- **Debug**: Choice resolution algorithm
- **Test**: Category filtering with updated references

### 2. Cache Investigation
- **Check**: Application-level caching
- **Verify**: Database query caching
- **Clear**: Any cached BEO output

### 3. Selection State Analysis
- **Investigate**: How BEO determines which choices are "selected"
- **Verify**: Choice detection logic with new reference structure

### 4. End-to-End Testing
- **Create**: Minimal test case with single combination
- **Trace**: Complete data flow from database to BEO output
- **Identify**: Exact point of failure in processing chain

---

## Scripts Created

### 1. `updateCombinationsToReferenceDuplicatedGroups()`
**Purpose**: Update combination items to reference (*) groups
**Status**: ✅ Successfully executed
**Impact**: 30 combinations updated

### 2. `deepUpdateInventoryGroupReferences()`
**Purpose**: Recursively update all nested inventory_group references
**Status**: ✅ Successfully executed  
**Impact**: 1,257 groups processed, hundreds of references updated

### 3. `repairMissingItemsArrays()`
**Purpose**: Copy items arrays from originals to duplicates
**Status**: ⚠️ Partially relevant (46 matches found)
**Impact**: Limited scope due to naming mismatches

---

## Critical Questions Remaining

1. **Why do database-verified fixes not reflect in BEO output?**
2. **Is there additional caching or processing layer not accounted for?**
3. **Are there other reference types beyond inventory_group that need updating?**
4. **Does the BEO merge tag use different data access patterns?**
5. **Are there selection state requirements not being met?**

---

*Investigation conducted: July 11, 2025*
*Total conversation length: ~200 exchanges*
*Scripts executed: 6 major repair attempts*
*Data integrity: Verified at database level*
*Issue status: Unresolved - requires deeper architectural analysis*
