/**
 * Inventory Migration Script
 * Queries all inventory items from the BEO debug log and generates SQL migration
 * Handles data_source_id mapping and ID consistency for local dev environment
 */

// All IDs from the BEO debug log
const MAIN_COMBINATION_ID = 20456859;
const INGREDIENT_GROUP_IDS = [20435097, 20434616, 20434630, 20434495, 20434052];

// All choice IDs from the debug log
const ALL_CHOICE_IDS = [
    // Salad Choice List choices
    20434612, 20434593, 20434595, 20434613, 20434614, 20434602, 20434592, 20434622,
    20434617, 20434620, 20434621, 20434629, 20434625, 20434626, 20434623, 20434633,
    20434632, 20434636, 20434637,

    // Buffet Entree Choice List choices
    20434990, 20434487, 20434498, 20434490, 20434472, 20434475, 20434471, 20434483,
    20434502, 20434478, 20434486, 20434477, 20434488, 20434481, 20434482, 20434474,
    20434479, 20434470, 20434493, 20434516, 20434500, 20434480, 20434484, 20434485,
    20434501, 20434491, 20434489, 20434499, 20433663,

    // Salad Serving Style choices
    20434586, 20434587, 20434590,

    // Sides Choice List choices (90 items)
    20434030, 20434001, 20434053, 20434020, 20433997, 20434017, 20434018, 20434116,
    20433999, 20433739, 20434023, 20434015, 20433998, 20434003, 20434021, 20434035,
    20434004, 20434014, 20434022, 20434000, 20434002, 20434007, 20434008, 20434026,
    20434010, 20434057, 20434103, 20434006, 20434024, 20434073, 20434012, 20434013,
    20434075, 20434016, 20434193, 20434029, 20434028, 20434027, 20434033, 20434019,
    20434025, 20434009, 20433827, 20434056, 20434110, 20434005, 20434064, 20434071,
    20434084, 20434087, 20434101, 20434076, 20433992, 20434081, 20434077, 20434037,
    20433991, 20434048, 20434112, 20434105, 20434109, 20432542, 20434104, 20434113,
    20434047, 20434145, 20434079, 20434118, 20434125, 20434126, 20434130, 20434152,
    20434160, 20434158, 20434161, 20434162, 20434166, 20434167, 20434168, 20434165,
    20434181, 20434092, 20434153, 20434171, 20434156, 20434050, 20434173
];

// Combine all IDs
const ALL_IDS = [MAIN_COMBINATION_ID, ...INGREDIENT_GROUP_IDS, ...ALL_CHOICE_IDS];

// Storage for all fetched data
let allInventoryData = [];
let processedCount = 0;

console.log('=== INVENTORY MIGRATION SCRIPT START ===');
console.log(`Total IDs to process: ${ALL_IDS.length}`);

/**
 * Process a single inventory item
 */
function processInventoryItem(id, callback) {
    // First try as inventory_billable_combinations
    databaseConnection.obj.getById('inventory_billable_combinations', id, function(combo) {
        if (combo && combo.id) {
            console.log(`✓ Found combination: ${combo.name} (ID: ${combo.id})`);
            allInventoryData.push({
                object_type: 'inventory_billable_combinations',
                data: combo
            });
            callback();
        } else {
            // Try as inventory_billable_groups
            databaseConnection.obj.getById('inventory_billable_groups', id, function(group) {
                if (group && group.id) {
                    console.log(`✓ Found group: ${group.name} (ID: ${group.id})`);
                    allInventoryData.push({
                        object_type: 'inventory_billable_groups',
                        data: group
                    });
                    callback();
                } else {
                    console.log(`✗ ID ${id} not found in either table`);
                    callback();
                }
            });
        }
    });
}

/**
 * Process all IDs sequentially
 */
function processAllItems() {
    if (processedCount >= ALL_IDS.length) {
        generateOutput();
        return;
    }

    const currentId = ALL_IDS[processedCount];
    console.log(`Processing ${processedCount + 1}/${ALL_IDS.length}: ID ${currentId}`);

    processInventoryItem(currentId, function() {
        processedCount++;
        // Add small delay to prevent overwhelming the database
        setTimeout(processAllItems, 100);
    });
}

/**
 * Generate JSON output and SQL migration
 */
function generateOutput() {
    console.log('\n=== PROCESSING COMPLETE ===');
    console.log(`Successfully fetched ${allInventoryData.length} records`);

    // Prepare data for migration
    const migrationData = allInventoryData.map(item => {
        const data = item.data;

        // Set data_source_id to the original ID
        const migrationRecord = {
            ...data,
            data_source_id: data.id, // Original ID becomes data_source_id
            // Keep the same ID for local dev consistency
            id: data.id
        };

        return {
            object_type: item.object_type,
            data: migrationRecord
        };
    });

    // Output JSON data
    console.log('\n=== JSON DATA FOR MIGRATION ===');
    console.log(JSON.stringify(migrationData, null, 2));

    // Generate SQL statements (both versions)
    generateSQLMigration(migrationData);
    generateEnhancedSQL(migrationData);
}

/**
 * Generate SQL INSERT statements for PostgreSQL
 */
function generateSQLMigration(migrationData) {
    console.log('\n=== SQL MIGRATION STATEMENTS ===');

    const combinationRecords = migrationData.filter(item => item.object_type === 'inventory_billable_combinations');
    const groupRecords = migrationData.filter(item => item.object_type === 'inventory_billable_groups');

    console.log(`\n-- Inventory Billable Combinations (${combinationRecords.length} records)`);
    combinationRecords.forEach(item => {
        const data = item.data;

        // Ensure the ID in the JSON data matches the table ID
        data.id = data.data_source_id;

        const jsonData = JSON.stringify(data).replace(/'/g, "''");

        console.log(`INSERT INTO inventory_billable_combinations (id, object_data) VALUES (${data.id}, '${jsonData}');`);
    });

    console.log(`\n-- Inventory Billable Groups (${groupRecords.length} records)`);
    groupRecords.forEach(item => {
        const data = item.data;

        // Ensure the ID in the JSON data matches the table ID
        data.id = data.data_source_id;

        const jsonData = JSON.stringify(data).replace(/'/g, "''");

        console.log(`INSERT INTO inventory_billable_groups (id, object_data) VALUES (${data.id}, '${jsonData}');`);
    });

    console.log('\n=== MIGRATION COMPLETE ===');
    console.log('Copy the SQL statements above and run them in your pgAdmin to seed your local dev database.');
    console.log('Note: Both the table ID and the JSON data ID are set to the original ID value.');
    console.log('The data_source_id field preserves the original ID for reference.');
}

/**
 * Enhanced SQL generation with better formatting and validation
 */
function generateEnhancedSQL(migrationData) {
    console.log('\n=== ENHANCED SQL MIGRATION ===');

    // Group by object type
    const combinationRecords = migrationData.filter(item => item.object_type === 'inventory_billable_combinations');
    const groupRecords = migrationData.filter(item => item.object_type === 'inventory_billable_groups');

    // Generate SQL with proper escaping and formatting
    let sqlStatements = [];

    // Add header comment
    sqlStatements.push('-- Inventory Migration Script');
    sqlStatements.push('-- Generated from BEO debug data');
    sqlStatements.push(`-- Total records: ${migrationData.length}`);
    sqlStatements.push(`-- Combinations: ${combinationRecords.length}, Groups: ${groupRecords.length}`);
    sqlStatements.push('-- Note: data_source_id is set to original ID, table ID matches JSON ID');
    sqlStatements.push('');

    // Process combinations
    if (combinationRecords.length > 0) {
        sqlStatements.push('-- Inventory Billable Combinations');
        combinationRecords.forEach(item => {
            const data = item.data;
            data.id = data.data_source_id; // Ensure consistency

            const jsonData = JSON.stringify(data)
                .replace(/\\/g, '\\\\')  // Escape backslashes
                .replace(/'/g, "''");    // Escape single quotes

            sqlStatements.push(`INSERT INTO inventory_billable_combinations (id, object_data) VALUES (${data.id}, '${jsonData}');`);
        });
        sqlStatements.push('');
    }

    // Process groups
    if (groupRecords.length > 0) {
        sqlStatements.push('-- Inventory Billable Groups');
        groupRecords.forEach(item => {
            const data = item.data;
            data.id = data.data_source_id; // Ensure consistency

            const jsonData = JSON.stringify(data)
                .replace(/\\/g, '\\\\')  // Escape backslashes
                .replace(/'/g, "''");    // Escape single quotes

            sqlStatements.push(`INSERT INTO inventory_billable_groups (id, object_data) VALUES (${data.id}, '${jsonData}');`);
        });
    }

    // Output all SQL statements
    console.log('\n=== COPY THIS SQL TO PGADMIN ===');
    sqlStatements.forEach(statement => console.log(statement));
    console.log('\n=== END SQL ===');
}

// Start processing
console.log('Starting inventory migration script...');
console.log('This will query all inventory items and generate migration SQL.');
processAllItems();
